'use client';

import clsx from 'clsx';
import { useEffect, useRef, useState, createRef } from 'react';
import { Save, Loader, Calendar, Mic, Sparkles } from 'lucide-react';

import Evaluation, { type EvaluationRef } from './components/Evaluation';
import StudentPicker from '@/components/StudentPicker';
import Upload, { type UploadRef, type FileType } from '@/components/UploadFile';

import { createObservation, getProjectList } from '@/api/pbl';
import { useRouter, useSearchParams } from 'next/navigation';
import { Toast, DatePicker, Picker, Popup, Selector } from 'antd-mobile';
import { useImmer } from 'use-immer';
import { format } from 'date-fns';
import { evaluationAtom } from '@/store/pbl';
import { useAtom } from 'jotai';
import SelectField from '../../material/create/components/SelectField';

interface Student {
  studentId: string;
  studentName: string;
  avatar: string;
  gender: number;
  classId?: string;
  abilities: Ability[];
}

interface MediaItem {
  type: 1 | 2;
  url: string;
  fileSize: number;
  cover: string;
  duration: number;
  videoPlayType: number;
}

interface Ability {
  id?: string;
  name?: string;
  icon?: React.ComponentType<{ className?: string }>;
  color?: 'blue' | 'purple' | 'green' | 'yellow' | 'red';
  abilityId?: string | number;
}

interface StudentEvaluation {
  studentId: string;
  studentName?: string;
  avatar?: string;
  gender?: number;
  deptId?: string;
  abilities: Ability[];
}

interface FormData {
  title: string;
  content: string;
  type: number;
  source: number;
  date: string;
  deptId: string;
  projectId: string;
  projectName: string;
  medias: MediaItem[];
  studentEvaluations: StudentEvaluation[];
}

export default function App() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const classId = searchParams?.get('classId');
  const [formData, setFormData] = useImmer<FormData>({
    title: '',
    content: '',
    type: 1,
    source: 2,
    deptId: classId || '',
    projectId: '',
    projectName: '',
    medias: [],
    date: format(new Date(), 'yyyy-MM-dd'),
    studentEvaluations: []
  });

  const [evaluationRefs, setEvaluationRefs] = useState<
    Record<string, React.RefObject<EvaluationRef | null>>
  >({});
  const [selectedStudents, setSelectedStudents] = useState<Student[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [evaluations, setEvaluation] = useAtom(evaluationAtom);
  const [projectList, setProjectList] = useState<
    Array<{ projectId: string; projectName: string; deptId: string }>
  >([]);

  const [projectPickerVisible, setProjectPickerVisible] = useState(false);

  // 语音转文字和AI重写相关状态
  const [isRecording, setIsRecording] = useState(false);
  const [isAiRewriting, setIsAiRewriting] = useState(false);
  const [aiRewritePopupVisible, setAiRewritePopupVisible] = useState(false);
  const [aiRequirement, setAiRequirement] = useState('');
  const [wordLimit, setWordLimit] = useState(300);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '创建观察记录';
    }
    // 清空 atom 数据
    setEvaluation({});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setEvaluation]);

  useEffect(() => {
    fetchProjectList();
  }, []);

  useEffect(() => {
    // 当学生列表变化时，为每个学生创建对应的 ref
    const newRefs: Record<string, React.RefObject<EvaluationRef | null>> = {};
    for (const student of formData.studentEvaluations) {
      newRefs[student.studentId] = createRef<EvaluationRef>();
    }
    setEvaluationRefs(newRefs);
  }, [formData.studentEvaluations]);

  const fetchProjectList = async () => {
    const response = await getProjectList({});
    // @ts-ignore
    const data = response.list || [];

    setProjectList(
      data.filter((item: { deptId: string }) => item.deptId === classId)
    );
  };

  const handleSubmit = () => {
    const currentFormData = JSON.parse(JSON.stringify(formData));

    // 转换评估数据为提交格式
    // currentFormData.studentEvaluations = Object.entries(allStudentEvaluations)
    //   .filter(([_, abilities]) => {
    //     // 检查是否有至少一个值为 true 的能力
    //     return Object.values(abilities).some((value) => value === true);
    //   })
    //   .map(([studentId, abilities]) => {
    //     // 只保留值为 true 的 ability
    //     const filteredAbilities = Object.entries(abilities)
    //       .filter(([_, value]) => value === true)
    //       .map(([abilityId]) => ({
    //         abilityId: Number.parseInt(abilityId)
    //       }));

    //     return {
    //       studentId,
    //       deptId: classId,
    //       abilities: filteredAbilities
    //     };
    //   });

    currentFormData.studentEvaluations = Object.entries(evaluations)
      .map(([studentId, abilitiesObj]) => {
        // 只保留 abilityId 为 34 并且为 true 的
        const abilities = Object.entries(abilitiesObj)
          .filter(([_, value]) => value === true)
          .map(([abilityId]) => ({ abilityId: Number(abilityId) }));

        // 如果 abilities 为空则不返回
        if (abilities.length === 0) return null;

        return {
          studentId,
          deptId: classId,
          abilities
        };
      })
      .filter(Boolean); // 去除空项

    // 输出处理后的数据以便调试
    console.log('处理后的学生评估数据：', currentFormData.studentEvaluations);

    if (uploadRef.current) {
      const files = uploadRef.current.getFiles();
      console.log('当前文件列表：', files);
      currentFormData.medias = files.map((item) => ({
        ...item,
        cover:
          item.type === 'video'
            ? `${item.url}?x-workflow-graph-name=video-thumbnail`
            : '',
        name: item.name || '未命名文件',
        fileSize: item.size || 0,
        type:
          item.type === 'image'
            ? 1
            : item.type === 'video'
              ? 2
              : item.type === 'audio'
                ? 3
                : 0
      }));
    }

    // 使用最新的数据进行后续操作
    if (
      !currentFormData.content ||
      !currentFormData.date ||
      !currentFormData.studentEvaluations.length
    ) {
      Toast.show({
        icon: 'fail',
        content: '请填写完整信息'
      });
      return;
    }
    setIsSubmitting(true);

    createObservation(currentFormData)
      .then((res) => {
        console.log(res);
        Toast.show({
          icon: 'success',
          content: '保存成功'
        });
        router.back();
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  // 定义 StudentPicker 返回的学生类型 (不含 abilities)
  type PickerStudent = Omit<Student, 'abilities'>;

  // 调整 handleStudentsSelect 的参数类型
  const handleStudentsSelect = (students: PickerStudent[]) => {
    // 将 PickerStudent[] 映射为 Student[]，添加空的 abilities
    const fullStudents: Student[] = students.map((s) => ({
      ...s,
      abilities: [] // 添加空的 abilities 数组
    }));
    setSelectedStudents(fullStudents); // 使用映射后的完整类型

    setFormData((draft) => {
      // draft.studentEvaluations 需要 studentId, studentName, avatar 等
      draft.studentEvaluations = students.map((student: PickerStudent) => ({
        studentId: student.studentId,
        deptId: student.classId,
        studentName: student.studentName,
        avatar: student.avatar, // 确保 avatar 存在
        gender: student.gender, // 确保 gender 存在
        abilities: [] // 初始化为空数组
      }));
    });
  };
  // Removed duplicate lines 218-222

  const uploadRef = useRef<UploadRef>(null);

  // 获取文件列表
  const handleGetFiles = () => {
    if (uploadRef.current) {
      const files = uploadRef.current.getFiles();
      console.log('当前文件列表：', files);
    }
  };

  // 清空文件列表
  const handleClearFiles = () => {
    uploadRef.current?.clearFiles();
  };

  // 处理语音转文字
  const handleVoiceToText = async () => {
    setIsRecording(true);
    try {
      // 模拟语音转文字过程
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // 模拟转换结果
      const mockVoiceText =
        '今天观察到小明在积木区域表现出很强的创造力，他能够独立搭建复杂的建筑结构，并且在遇到困难时会主动寻求帮助。';

      setFormData((draft) => {
        draft.content = draft.content
          ? `${draft.content}\n${mockVoiceText}`
          : mockVoiceText;
      });

      Toast.show({
        icon: 'success',
        content: '语音转文字完成'
      });
    } catch (error) {
      Toast.show({
        icon: 'fail',
        content: '语音转文字失败'
      });
    } finally {
      setIsRecording(false);
    }
  };

  // 处理AI重写
  const handleAiRewrite = async () => {
    if (!formData.content.trim()) {
      Toast.show({
        icon: 'fail',
        content: '请先输入内容'
      });
      return;
    }

    setAiRewritePopupVisible(true);
  };

  // 提交AI重写要求
  const handleAiRewriteSubmit = async () => {
    setAiRewritePopupVisible(false);
    setIsAiRewriting(true);

    try {
      // 模拟AI重写过程
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // 模拟AI重写结果，结合用户的额外要求
      const mockAiRewriteText = `【观察记录】
时间：${formData.date}
观察对象：班级学生
观察内容：${formData.content}

【行为分析】
通过本次观察，发现学生在学习过程中展现出积极的探索精神和良好的合作能力。建议继续鼓励此类行为的发展。
${aiRequirement ? `\n【用户要求】\n${aiRequirement}` : ''}

【教育建议】
1. 提供更多动手操作的机会
2. 鼓励学生之间的互动交流
3. 及时给予正面反馈和指导`;

      setFormData((draft) => {
        draft.content = mockAiRewriteText;
      });

      Toast.show({
        icon: 'success',
        content: 'AI重写完成'
      });
    } catch (error) {
      Toast.show({
        icon: 'fail',
        content: 'AI重写失败'
      });
    } finally {
      setIsAiRewriting(false);
    }
  };

  return (
    <main className="">
      <div className="p-4 pb-0">
        <StudentPicker
          // onMultiSelect 现在接收 PickerStudent[] 类型
          onMultiSelect={handleStudentsSelect}
          // multiValue 可能也需要调整类型，暂时保持不变
          multiValue={selectedStudents}
          placeholder="请选择班级和学生"
          multiple={true}
          classId={classId ?? undefined}
        />
      </div>
      <div className="flex-1 overflow-y-auto p-4">
        <div className="fade-in">
          <div className="mb-0">
            <label
              htmlFor="observation"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              观察记录描述
            </label>
            <div className="relative">
              <textarea
                id="observation"
                name="observation"
                value={formData.content}
                onChange={(e) =>
                  setFormData((draft) => {
                    draft.content = e.target.value;
                  })
                }
                placeholder="以客观、具体描述的方式记录观察过程"
                rows={8}
                className="w-full p-4 pb-16 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none text-gray-700 leading-relaxed"
                style={{ minHeight: '200px' }}
              />

              {/* 底部工具栏 */}
              <div className="absolute bottom-3 left-3 right-3 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {/* 语音转文字按钮 */}
                  <button
                    type="button"
                    onClick={handleVoiceToText}
                    disabled={isRecording}
                    className={clsx(
                      'flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium transition-all',
                      isRecording
                        ? 'bg-green-100 text-indigo-600 cursor-not-allowed'
                        : 'bg-indigo-50 text-indigo-600 hover:bg-indigo-100 active:scale-95'
                    )}
                  >
                    <Mic
                      className={clsx(
                        'w-4 h-4',
                        isRecording && 'animate-pulse'
                      )}
                    />
                    {isRecording ? '录音中...' : '语音转文字'}
                  </button>

                  {/* AI重写按钮 */}
                  <button
                    type="button"
                    onClick={handleAiRewrite}
                    disabled={isAiRewriting || !formData.content.trim()}
                    className={clsx(
                      'flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium transition-all',
                      isAiRewriting
                        ? 'bg-indigo-100 text-indigo-600 cursor-not-allowed'
                        : formData.content.trim()
                          ? 'bg-indigo-50 text-indigo-600 hover:bg-indigo-100 active:scale-95'
                          : 'bg-gray-50 text-gray-400 cursor-not-allowed'
                    )}
                  >
                    <Sparkles
                      className={clsx(
                        'w-4 h-4',
                        isAiRewriting && 'animate-spin'
                      )}
                    />
                    {isAiRewriting ? 'AI重写中...' : 'AI重写'}
                  </button>
                </div>

                {/* 字数统计 */}
                <div className="text-xs text-gray-400">
                  {formData.content.length}/3000
                </div>
              </div>
            </div>
          </div>
          <div className="mb-4">
            <label
              htmlFor="date"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              记录日期
            </label>
            <div
              id="date"
              onClick={() => setDatePickerVisible(true)}
              className="w-full p-2 border border-gray-300 rounded-md flex items-center justify-between text-gray-700"
            >
              {formData.date}
              <Calendar className="w-4 h-4" />
            </div>
          </div>
        </div>
        {projectList.length > 0 && (
          <div className="mb-4">
            <SelectField
              label="关联 PBL 项目"
              value={formData.projectName || ''}
              placeholder="请选择 PBL 项目"
              onClick={() => setProjectPickerVisible(true)}
            />
          </div>
        )}
        <div className="fade-in mb-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">
            关联能力
            <span className="text-xs text-gray-500 ml-2">
              (每个学生可单独选择)
            </span>
          </h3>
          {formData.studentEvaluations.length === 0 ? (
            <div className="text-center py-4 text-gray-500">请先选择学生</div>
          ) : (
            <div className="space-y-6">
              {formData.studentEvaluations.map((child, index) => (
                <div
                  key={child.studentId || index}
                  className="border rounded-lg p-3"
                >
                  <div className="flex items-center mb-3">
                    <img
                      src={child.avatar}
                      alt={child.studentName}
                      className="w-8 h-8 rounded-full mr-2"
                    />
                    <span className="font-medium">{child.studentName}</span>
                    {/* <span className="ml-2 text-sm text-gray-500">
                      ({getChildAbilityCount(index)}/{abilityList.length})
                    </span> */}
                  </div>
                  {/* 传递 studentId 给 Evaluation 组件 */}
                  <Evaluation
                    ref={evaluationRefs[child.studentId]}
                    studentId={child.studentId}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="fade-in">
          <label
            htmlFor="observation"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            关联文件
          </label>
          <Upload
            ref={uploadRef}
            initialFiles={formData.medias as unknown as FileType[]}
            className="min-h-[400px]"
          />
        </div>
      </div>

      <div className="p-4 border-t border-gray-200 bg-white">
        <button
          type="button"
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.content}
          className={clsx(
            'w-full py-3 rounded-md flex items-center justify-center gap-2',
            isSubmitting || !formData.content
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-indigo-500 text-white hover:bg-primary/90'
          )}
        >
          {isSubmitting ? (
            <>
              <Loader className="w-4 h-4 animate-spin" />
              提交中...
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              保存观察记录
            </>
          )}
        </button>
      </div>
      <DatePicker
        title="时间选择"
        visible={datePickerVisible}
        onClose={() => {
          setDatePickerVisible(false);
        }}
        max={new Date()}
        onConfirm={(val) => {
          setFormData((draft) => {
            draft.date = format(val, 'yyyy-MM-dd');
          });
        }}
      />
      {/* PBL 项目选择 */}
      <Picker
        columns={[
          projectList.map((project) => ({
            label: project.projectName,
            value: project.projectId
          }))
        ]}
        visible={projectPickerVisible}
        onClose={() => {
          setProjectPickerVisible(false);
        }}
        onConfirm={(v, c) => {
          if (v[0]) {
            setFormData((draft) => {
              draft.projectId = (c.items[0]?.value as string) || '';
              draft.projectName = (c.items[0]?.label as string) || '';
            });
          }
        }}
      />

      {/* AI重写要求弹窗 */}
      <Popup
        visible={aiRewritePopupVisible}
        onMaskClick={() => setAiRewritePopupVisible(false)}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
          padding: '20px'
        }}
      >
        <div className="space-y-4">
          {/* 标题 */}
          <div className="text-lg font-semibold text-gray-900">
            表达你想要AI优化的想法:
          </div>

          {/* 输入框 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <textarea
              value={aiRequirement}
              onChange={(e) => setAiRequirement(e.target.value)}
              placeholder="这份幼儿园观察实录，请用客观写实的方式优化一下"
              className="w-full h-32 bg-transparent border-none outline-none resize-none text-gray-700 placeholder-gray-400"
              maxLength={100}
            />
            <div className="flex justify-between items-center mt-2">
              <button
                type="button"
                onClick={() => setAiRequirement('')}
                className="text-xs text-gray-400 hover:text-gray-600"
              >
                清空
              </button>
              <div className="text-xs text-gray-400">
                {aiRequirement.length}/100
              </div>
            </div>
          </div>

          {/* 字数限制选择 */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">字数限制:</span>
            <Selector
              options={[
                { label: '300', value: 300 },
                { label: '500', value: 500 },
                { label: '800', value: 800 },
                { label: '1000', value: 1000 }
              ]}
              value={[wordLimit]}
              onChange={(val) => setWordLimit(val[0] as number)}
              style={{
                '--border-radius': '20px',
                '--border': '1px solid #e5e7eb',
                '--padding': '8px 16px'
              }}
            />
          </div>

          {/* 提交按钮 */}
          <button
            type="button"
            onClick={handleAiRewriteSubmit}
            className="w-full bg-green-500 text-white py-3 rounded-lg font-medium hover:bg-green-600 active:scale-95 transition-all"
          >
            提交
          </button>
        </div>
      </Popup>
    </main>
  );
}
